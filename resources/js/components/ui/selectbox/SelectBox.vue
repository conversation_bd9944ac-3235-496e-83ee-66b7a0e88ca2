<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { SelectRoot, type SelectRootProps } from 'reka-ui'
import { computed } from 'vue'

export interface SelectBoxOption {
  value: string | number
  label: string
  disabled?: boolean
}

interface Props extends SelectRootProps {
  class?: HTMLAttributes['class']
  options?: SelectBoxOption[]
  placeholder?: string
  showLabel?: boolean
  label?: string
  showClear?: boolean
  showRefresh?: boolean
  refreshLoading?: boolean
  disabled?: boolean
  error?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showLabel: true,
  showClear: true,
  showRefresh: false,
  refreshLoading: false,
  disabled: false,
  error: false,
  placeholder: '...',
})

const emits = defineEmits<{
  'update:modelValue': [value: any]
  'refresh': []
  'clear': []
}>()

const delegatedProps = computed(() => {
  const { class: _, options: __, placeholder: ___, showLabel: ____, label: _____, showClear: ______, showRefresh: _______, refreshLoading: ________, error: _________, ...delegated } = props
  return delegated
})
</script>

<template>
  <div :class="cn('space-y-2', props.class)">
    <!-- Label -->
    <label 
      v-if="showLabel && label" 
      :class="cn(
        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        error ? 'text-destructive' : 'text-foreground'
      )"
    >
      {{ label }}
    </label>

    <!-- Select Component -->
    <SelectRoot v-bind="delegatedProps" @update:model-value="emits('update:modelValue', $event)">
      <slot />
    </SelectRoot>
  </div>
</template>
