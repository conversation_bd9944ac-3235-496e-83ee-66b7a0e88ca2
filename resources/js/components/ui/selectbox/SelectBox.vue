<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { Select, type SelectRootProps } from 'reka-ui'
import { computed } from 'vue'

export interface SelectBoxOption {
  value: string | number
  label: string
  disabled?: boolean
}

interface Props extends SelectRootProps {
  class?: HTMLAttributes['class']
  options?: SelectBoxOption[]
  placeholder?: string
  showLabel?: boolean
  label?: string
  showClear?: boolean
  showRefresh?: boolean
  refreshLoading?: boolean
  disabled?: boolean
  error?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showLabel: true,
  showClear: true,
  showRefresh: false,
  refreshLoading: false,
  disabled: false,
  error: false,
  placeholder: 'Select an option...',
})

const emits = defineEmits<{
  'update:modelValue': [value: string | number | undefined]
  'refresh': []
  'clear': []
}>()

const delegatedProps = computed(() => {
  const { class: _, options: __, placeholder: ___, showLabel: ____, label: _____, showClear: ______, showRefresh: _______, refreshLoading: ________, error: _________, ...delegated } = props
  return delegated
})

const handleClear = () => {
  emits('update:modelValue', undefined)
  emits('clear')
}

const handleRefresh = () => {
  emits('refresh')
}
</script>

<template>
  <div :class="cn('space-y-2', props.class)">
    <!-- Label -->
    <label 
      v-if="showLabel && label" 
      :class="cn(
        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        error ? 'text-destructive' : 'text-foreground'
      )"
    >
      {{ label }}
    </label>

    <!-- Select Component -->
    <Select v-bind="delegatedProps" @update:model-value="emits('update:modelValue', $event)">
      <slot />
    </Select>
  </div>
</template>
