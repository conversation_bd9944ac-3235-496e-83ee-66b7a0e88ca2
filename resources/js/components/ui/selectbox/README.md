# SelectBox Component

Vue Component Select Box dựa trên Reka UI với đầy đủ tính năng và hỗ trợ light/dark mode.

## Tính năng

✅ **Options qua props**: Nhận array options với `{ value, label, disabled }`  
✅ **Tắt/bật label**: Props `showLabel` và `label`  
✅ **Clear giá trị**: Nút X để xóa selection  
✅ **Refresh button**: Props `showRefresh` với loading state  
✅ **Emit events**: `refresh`, `clear`, `update:modelValue`  
✅ **Light/Dark mode**: Sử dụng CSS variables của Tailwind  
✅ **Multiple sizes**: `sm`, `default`, `lg`  
✅ **Error state**: Props `error` cho validation  
✅ **Disabled state**: Props `disabled`  

## Cách sử dụng

### Import

```typescript
import { SelectBoxComplete, type SelectBoxOption } from '@/components/ui/selectbox'
```

### Sử dụng cơ bản

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { SelectBoxComplete, type SelectBoxOption } from '@/components/ui/selectbox'

const selectedValue = ref<string | number>()

const options: SelectBoxOption[] = [
  { value: '1', label: 'Option 1' },
  { value: '2', label: 'Option 2' },
  { value: '3', label: 'Option 3' },
  { value: '4', label: 'Option 4 (Disabled)', disabled: true },
]
</script>

<template>
  <SelectBoxComplete
    v-model="selectedValue"
    label="Choose an option"
    :options="options"
    placeholder="Select your option..."
  />
</template>
```

### Với Refresh Button

```vue
<script setup lang="ts">
const refreshLoading = ref(false)

const handleRefresh = () => {
  refreshLoading.value = true
  // Simulate API call
  setTimeout(() => {
    // Reload options logic here
    refreshLoading.value = false
  }, 1500)
}
</script>

<template>
  <SelectBoxComplete
    v-model="selectedValue"
    label="Options with refresh"
    :options="options"
    show-refresh
    :refresh-loading="refreshLoading"
    @refresh="handleRefresh"
  />
</template>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `modelValue` | `string \| number` | `undefined` | Giá trị đã chọn |
| `options` | `SelectBoxOption[]` | `[]` | Danh sách options |
| `placeholder` | `string` | `'Select an option...'` | Placeholder text |
| `label` | `string` | `undefined` | Label text |
| `showLabel` | `boolean` | `true` | Hiển thị label |
| `showClear` | `boolean` | `true` | Hiển thị nút clear |
| `showRefresh` | `boolean` | `false` | Hiển thị nút refresh |
| `refreshLoading` | `boolean` | `false` | Trạng thái loading của refresh |
| `disabled` | `boolean` | `false` | Disable component |
| `error` | `boolean` | `false` | Trạng thái error |
| `variant` | `'default' \| 'error'` | `'default'` | Variant style |
| `size` | `'sm' \| 'default' \| 'lg'` | `'default'` | Kích thước |

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `update:modelValue` | `any` | Khi giá trị thay đổi |
| `refresh` | `[]` | Khi click nút refresh |
| `clear` | `[]` | Khi click nút clear |

## SelectBoxOption Interface

```typescript
interface SelectBoxOption {
  value: string | number
  label: string
  disabled?: boolean
}
```

## Ví dụ nâng cao

Xem file `SelectBoxDemo.vue` để có các ví dụ chi tiết về:
- SelectBox cơ bản
- SelectBox với refresh
- SelectBox không có label
- SelectBox không có clear
- Các kích thước khác nhau
- Error state
- Disabled state

## Styling

Component sử dụng Tailwind CSS với class-variance-authority và hỗ trợ dark mode tự động thông qua CSS variables.

## Components con

- `SelectBox`: Component wrapper chính
- `SelectBoxTrigger`: Trigger button với clear/refresh
- `SelectBoxValue`: Hiển thị giá trị đã chọn
- `SelectBoxContent`: Dropdown content với portal
- `SelectBoxItem`: Option items với indicator
- `SelectBoxComplete`: Component tổng hợp dễ sử dụng
