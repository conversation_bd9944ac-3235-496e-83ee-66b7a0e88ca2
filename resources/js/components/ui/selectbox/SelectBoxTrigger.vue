<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { SelectTrigger, SelectIcon, type SelectTriggerProps } from 'reka-ui'
import { ChevronDown, X, RefreshCw } from 'lucide-vue-next'
import { computed } from 'vue'
import { type SelectBoxVariants, selectBoxVariants } from '.'

interface Props extends SelectTriggerProps {
  class?: HTMLAttributes['class']
  variant?: SelectBoxVariants['variant']
  size?: SelectBoxVariants['size']
  showClear?: boolean
  showRefresh?: boolean
  refreshLoading?: boolean
  hasValue?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'default',
  showClear: true,
  showRefresh: false,
  refreshLoading: false,
  hasValue: false,
})

const emits = defineEmits<{
  'clear': []
  'refresh': []
}>()

const delegatedProps = computed(() => {
  const { class: _, variant: __, size: ___, showClear: ____, showRefresh: _____, refreshLoading: ______, hasValue: _______, ...delegated } = props
  return delegated
})

const handleClear = (e: Event) => {
  e.stopPropagation()
  emits('clear')
}

const handleRefresh = (e: Event) => {
  e.stopPropagation()
  emits('refresh')
}
</script>

<template>
  <SelectTrigger
    v-bind="delegatedProps"
    :class="cn(selectBoxVariants({ variant, size }), 'flex items-center justify-between', props.class)"
  >
    <div class="flex items-center flex-1 min-w-0">
      <slot />
    </div>
    
    <div class="flex items-center gap-1 ml-2">
      <!-- Clear Button -->
      <button
        v-if="showClear && hasValue"
        type="button"
        @click="handleClear"
        :class="cn(
          'inline-flex items-center justify-center rounded-sm w-4 h-4 text-muted-foreground hover:text-foreground transition-colors',
          'hover:bg-muted'
        )"
      >
        <X class="h-3 w-3" />
      </button>

      <!-- Refresh Button -->
      <button
        v-if="showRefresh"
        type="button"
        @click="handleRefresh"
        :disabled="refreshLoading"
        :class="cn(
          'inline-flex items-center justify-center rounded-sm w-4 h-4 text-muted-foreground hover:text-foreground transition-colors',
          'hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed',
          refreshLoading && 'animate-spin'
        )"
      >
        <RefreshCw class="h-3 w-3" />
      </button>

      <!-- Chevron Down -->
      <SelectIcon>
        <ChevronDown class="h-4 w-4 text-muted-foreground" />
      </SelectIcon>
    </div>
  </SelectTrigger>
</template>
