<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { computed } from 'vue'
import { type SelectBoxVariants } from '.'
import SelectBox from './SelectBox.vue'
import SelectBoxTrigger from './SelectBoxTrigger.vue'
import SelectBoxValue from './SelectBoxValue.vue'
import SelectBoxContent from './SelectBoxContent.vue'
import SelectBoxItem from './SelectBoxItem.vue'

export interface SelectBoxOption {
  value: string | number
  label: string
  disabled?: boolean
}

interface Props {
  modelValue?: string | number
  class?: HTMLAttributes['class']
  options?: SelectBoxOption[]
  placeholder?: string
  showLabel?: boolean
  label?: string
  showClear?: boolean
  showRefresh?: boolean
  refreshLoading?: boolean
  disabled?: boolean
  error?: boolean
  variant?: SelectBoxVariants['variant']
  size?: SelectBoxVariants['size']
}

const props = withDefaults(defineProps<Props>(), {
  showLabel: true,
  showClear: true,
  showRefresh: false,
  refreshLoading: false,
  disabled: false,
  error: false,
  placeholder: 'Select an option...',
  variant: 'default',
  size: 'default',
  options: () => [],
})

const emits = defineEmits<{
  'update:modelValue': [value: any]
  'refresh': []
  'clear': []
}>()

const hasValue = computed(() => {
  return props.modelValue !== undefined && props.modelValue !== null && props.modelValue !== ''
})

const handleClear = () => {
  emits('update:modelValue', undefined)
  emits('clear')
}

const handleRefresh = () => {
  emits('refresh')
}
</script>

<template>
  <div :class="cn('w-full', props.class)">
    <SelectBox
      :model-value="modelValue"
      :show-label="showLabel"
      :label="label"
      :show-clear="showClear"
      :show-refresh="showRefresh"
      :refresh-loading="refreshLoading"
      :disabled="disabled"
      :error="error"
      @update:model-value="emits('update:modelValue', $event)"
      @refresh="handleRefresh"
      @clear="handleClear"
    >
      <SelectBoxTrigger
        :variant="error ? 'error' : variant"
        :size="size"
        :show-clear="showClear"
        :show-refresh="showRefresh"
        :refresh-loading="refreshLoading"
        :has-value="hasValue"
        :disabled="disabled"
        @clear="handleClear"
        @refresh="handleRefresh"
      >
        <SelectBoxValue :placeholder="placeholder" />
      </SelectBoxTrigger>

      <SelectBoxContent>
        <SelectBoxItem
          v-for="option in options"
          :key="option.value"
          :value="String(option.value)"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </SelectBoxItem>
        
        <!-- Empty state -->
        <div
          v-if="!options || options.length === 0"
          class="py-6 text-center text-sm text-muted-foreground"
        >
          No options available
        </div>
      </SelectBoxContent>
    </SelectBox>
  </div>
</template>
