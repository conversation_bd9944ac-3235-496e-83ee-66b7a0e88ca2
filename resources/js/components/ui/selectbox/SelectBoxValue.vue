<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { SelectValue, type SelectValueProps } from 'reka-ui'
import { computed } from 'vue'

interface Props extends SelectValueProps {
  class?: HTMLAttributes['class']
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Select an option...',
})

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props
  return delegated
})
</script>

<template>
  <SelectValue
    v-bind="delegatedProps"
    :class="cn('text-left truncate', props.class)"
    :placeholder="placeholder"
  >
    <slot />
  </SelectValue>
</template>
