<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { 
  SelectRoot, 
  SelectTrigger, 
  SelectValue, 
  SelectIcon,
  SelectPortal,
  SelectContent,
  SelectViewport,
  SelectItem,
  SelectItemText,
  SelectItemIndicator
} from 'reka-ui'
import { ChevronDown, X, RefreshCw, Check } from 'lucide-vue-next'
import { computed } from 'vue'
import { type SelectBoxVariants, selectBoxVariants } from '.'

export interface SelectBoxOption {
  value: string | number
  label: string
  disabled?: boolean
}

interface Props {
  modelValue?: string | number
  class?: HTMLAttributes['class']
  options?: SelectBoxOption[]
  placeholder?: string
  showLabel?: boolean
  label?: string
  showClear?: boolean
  showRefresh?: boolean
  refreshLoading?: boolean
  disabled?: boolean
  error?: boolean
  variant?: SelectBoxVariants['variant']
  size?: SelectBoxVariants['size']
}

const props = withDefaults(defineProps<Props>(), {
  showLabel: true,
  showClear: true,
  showRefresh: false,
  refreshLoading: false,
  disabled: false,
  error: false,
  placeholder: 'Select an option...',
  variant: 'default',
  size: 'default',
  options: () => [],
})

const emits = defineEmits<{
  'update:modelValue': [value: any]
  'refresh': []
  'clear': []
}>()

const hasValue = computed(() => {
  return props.modelValue !== undefined && props.modelValue !== null && props.modelValue !== ''
})

const handleClear = (e: Event) => {
  e.stopPropagation()
  emits('update:modelValue', undefined)
  emits('clear')
}

const handleRefresh = (e: Event) => {
  e.stopPropagation()
  emits('refresh')
}
</script>

<template>
  <div :class="cn('w-full space-y-2', props.class)">
    <!-- Label -->
    <label 
      v-if="showLabel && label" 
      :class="cn(
        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        error ? 'text-destructive' : 'text-foreground'
      )"
    >
      {{ label }}
    </label>

    <!-- Select Component -->
    <SelectRoot 
      :model-value="String(modelValue || '')"
      :disabled="disabled"
      @update:model-value="emits('update:modelValue', $event)"
    >
      <SelectTrigger
        :class="cn(
          selectBoxVariants({ variant: error ? 'error' : variant, size }),
          'flex items-center justify-between'
        )"
      >
        <div class="flex items-center flex-1 min-w-0">
          <SelectValue :placeholder="placeholder" />
        </div>
        
        <div class="flex items-center gap-1 ml-2">
          <!-- Clear Button -->
          <button
            v-if="showClear && hasValue"
            type="button"
            @click="handleClear"
            :class="cn(
              'inline-flex items-center justify-center rounded-sm w-4 h-4 text-muted-foreground hover:text-foreground transition-colors',
              'hover:bg-muted'
            )"
          >
            <X class="h-3 w-3" />
          </button>

          <!-- Refresh Button -->
          <button
            v-if="showRefresh"
            type="button"
            @click="handleRefresh"
            :disabled="refreshLoading"
            :class="cn(
              'inline-flex items-center justify-center rounded-sm w-4 h-4 text-muted-foreground hover:text-foreground transition-colors',
              'hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed',
              refreshLoading && 'animate-spin'
            )"
          >
            <RefreshCw class="h-3 w-3" />
          </button>

          <!-- Chevron Down -->
          <SelectIcon>
            <ChevronDown class="h-4 w-4 text-muted-foreground" />
          </SelectIcon>
        </div>
      </SelectTrigger>

      <SelectPortal>
        <SelectContent
          :class="cn(
            'relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md',
            'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
            'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
            'data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2',
            'data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2'
          )"
        >
          <SelectViewport class="p-1">
            <SelectItem
              v-for="option in options"
              :key="option.value"
              :value="String(option.value)"
              :disabled="option.disabled"
              :class="cn(
                'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none',
                'focus:bg-accent focus:text-accent-foreground',
                'data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
                'data-[highlighted]:bg-accent data-[highlighted]:text-accent-foreground'
              )"
            >
              <span class="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                <SelectItemIndicator>
                  <Check class="h-4 w-4" />
                </SelectItemIndicator>
              </span>
              
              <SelectItemText>
                {{ option.label }}
              </SelectItemText>
            </SelectItem>
            
            <!-- Empty state -->
            <div
              v-if="!options || options.length === 0"
              class="py-6 text-center text-sm text-muted-foreground"
            >
              No options available
            </div>
          </SelectViewport>
        </SelectContent>
      </SelectPortal>
    </SelectRoot>
  </div>
</template>
